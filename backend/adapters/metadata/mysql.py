#!/usr/bin/env python3
"""
Database operations module for managing repository information
"""

import os
import pymysql
from typing import List, Dict, Any, Optional
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

DB_CONNECTION_ARGS = {
    "host": "127.0.0.1",
    "port": 2881,
    "user": "root@test",
    "password": "admin",
    "database": "doc2dev",
    "charset": "utf8mb4"
}

def get_db_connection():
    """
    Get database connection
    
    Returns:
        pymysql.Connection: Database connection object
    """
    try:
        connection = pymysql.connect(**DB_CONNECTION_ARGS)
        return connection
    except Exception as e:
        print(f"Database connection failed: {str(e)}")
        raise

def get_all_repositories():
    """
    Get all repository information
    
    Returns:
        List[Dict]: List of repository information
    """
    try:
        connection = get_db_connection()
        with connection.cursor(pymysql.cursors.DictCursor) as cursor:
            sql = """
            SELECT id, name, description, repo, repo_url, 
                   tokens, snippets, repo_status, created_at, updated_at
            FROM repositories
            ORDER BY name
            """
            
            # Execute original query
            cursor.execute(sql)
            repositories = cursor.fetchall()
            
            return repositories
    except Exception as e:
        print(f"Failed to get repository information: {str(e)}")
        return []
    finally:
        if connection:
            connection.close()

def get_repository_by_name(name: str):
    """
    Get repository information by name
    
    Args:
        name: Repository name
        
    Returns:
        Dict: Repository information
    """
    try:
        connection = get_db_connection()
        with connection.cursor(pymysql.cursors.DictCursor) as cursor:
            sql = """
            SELECT id, name, description, repo, repo_url, 
                   tokens, snippets, repo_status, created_at, updated_at
            FROM repositories
            WHERE name = %s
            """
            cursor.execute(sql, (name,))
            repository = cursor.fetchone()
            
            return repository
    except Exception as e:
        print(f"Failed to get repository information: {str(e)}")
        return None
    finally:
        if connection:
            connection.close()

def get_repository_by_path(repo_path: str):
    """
    Get repository information by repository path
    
    Args:
        repo_path: Repository path in format /owner/repo
        
    Returns:
        Dict: Repository information, returns None if not found
    """
    try:
        connection = get_db_connection()
        with connection.cursor(pymysql.cursors.DictCursor) as cursor:
            sql = """
            SELECT id, name, description, repo, repo_url, 
                   tokens, snippets, repo_status, created_at, updated_at
            FROM repositories
            WHERE repo = %s
            """
            cursor.execute(sql, (repo_path,))
            repository = cursor.fetchone()
            
            return repository
    except Exception as e:
        print(f"Failed to get repository information by path: {str(e)}")
        return None
    finally:
        if connection:
            connection.close()

def add_repository(name: str, description: str, repo: str, repo_url: str, repo_status: str, tokens: int = 0, snippets: int = 0):
    """
    Add repository information
    
    Args:
        name: Repository name
        description: Repository description
        repo: Repository path
        repo_url: Repository URL
        repo_status: Repository status, options: 'in_progress', 'completed', 'failed', 'pending'
        tokens: Number of tokens in documents, default 0
        snippets: Number of code blocks in documents, default 0
        
    Returns:
        bool: Whether addition was successful
    """
    try:
        connection = get_db_connection()
        with connection.cursor() as cursor:
            sql = """
            INSERT INTO repositories (name, description, repo, repo_url, repo_status, tokens, snippets)
            VALUES (%s, %s, %s, %s, %s, %s, %s)
            """
            cursor.execute(sql, (name, description, repo, repo_url, repo_status, tokens, snippets))
            connection.commit()
            return True
    except Exception as e:
        print(f"Failed to add repository information: {str(e)}")
        return False
    finally:
        if connection:
            connection.close()

def update_repository(id: int, name: str, description: str, repo: str, repo_url: str):
    """
    Update repository information
    
    Args:
        id: Repository ID
        name: Repository name
        description: Repository description
        repo: Repository path
        repo_url: Repository URL
        
    Returns:
        bool: Whether update was successful
    """
    try:
        connection = get_db_connection()
        with connection.cursor() as cursor:
            sql = """
            UPDATE repositories
            SET name = %s, description = %s, repo = %s, repo_url = %s
            WHERE id = %s
            """
            cursor.execute(sql, (name, description, repo, repo_url, id))
            connection.commit()
            return True
    except Exception as e:
        print(f"Failed to update repository information: {str(e)}")
        return False
    finally:
        if connection:
            connection.close()

def delete_repository(id: int):
    """
    Delete repository information
    
    Args:
        id: Repository ID
        
    Returns:
        bool: Whether deletion was successful
    """
    try:
        connection = get_db_connection()
        with connection.cursor() as cursor:
            sql = """
            DELETE FROM repositories
            WHERE id = %s
            """
            cursor.execute(sql, (id,))
            connection.commit()
            return True
    except Exception as e:
        print(f"Failed to delete repository information: {str(e)}")
        return False
    finally:
        if connection:
            connection.close()

def delete_vector_table(table_name: str):
    """
    Delete vector table
    
    Args:
        table_name: Name of vector table to delete
        
    Returns:
        bool: Whether deletion was successful
    """
    try:
        connection = get_db_connection()
        with connection.cursor() as cursor:
            # Safely format table name
            safe_table_name = table_name.replace('-', '_')
            sql = f"DROP TABLE IF EXISTS {safe_table_name}"
            cursor.execute(sql)
            connection.commit()
            return True
    except Exception as e:
        print(f"Failed to delete vector table: {str(e)}")
        return False
    finally:
        if connection:
            connection.close()

def get_repository_by_id(id: int):
    """
    Get repository information by ID
    
    Args:
        id: Repository ID
        
    Returns:
        Dict: Repository information, returns None if not found
    """
    try:
        connection = get_db_connection()
        with connection.cursor(pymysql.cursors.DictCursor) as cursor:
            sql = """
            SELECT id, name, description, repo, repo_url, 
                   tokens, snippets, repo_status, created_at, updated_at
            FROM repositories
            WHERE id = %s
            """
            cursor.execute(sql, (id,))
            result = cursor.fetchone()
            return result
    except Exception as e:
        print(f"Failed to get repository information: {str(e)}")
        return None
    finally:
        if connection:
            connection.close()


def update_repository_status(id: int, repo_status: str):
    """
    Update repository status
    
    Args:
        id: Repository ID
        repo_status: Repository status, options: 'in_progress', 'completed', 'failed', 'pending'
        
    Returns:
        bool: Whether update was successful
    """
    try:
        connection = get_db_connection()
        with connection.cursor() as cursor:
            sql = """
            UPDATE repositories
            SET repo_status = %s
            WHERE id = %s
            """
            cursor.execute(sql, (repo_status, id))
            connection.commit()
            return True
    except Exception as e:
        print(f"Failed to update repository status: {str(e)}")
        return False
    finally:
        if connection:
            connection.close()

def update_repository_counts(id: int, tokens: int, snippets: int):
    """
    Update repository token and code snippet counts
    
    Args:
        id: Repository ID
        tokens: Number of tokens
        snippets: Number of code snippets
        
    Returns:
        bool: Whether update was successful
    """
    try:
        connection = get_db_connection()
        with connection.cursor() as cursor:
            sql = """
            UPDATE repositories
            SET tokens = %s, snippets = %s
            WHERE id = %s
            """
            cursor.execute(sql, (tokens, snippets, id))
            connection.commit()
            return True
    except Exception as e:
        print(f"Failed to update repository counts: {str(e)}")
        return False
    finally:
        if connection:
            connection.close()
